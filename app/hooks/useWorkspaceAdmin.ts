"use client";

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { getWorkspaceInfo } from '@/app/actions/workspace';

/**
 * 检查当前用户是否为指定工作空间的管理员（owner 或 admin）
 * @param workspaceId - 工作空间ID
 * @returns {isAdmin: boolean, isLoading: boolean, error: string | null}
 */
export function useWorkspaceAdmin(workspaceId: string | null) {
  const { data: session, status } = useSession();
  const [isAdmin, setIsAdmin] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function checkAdminStatus() {
      // 如果没有session或workspaceId，直接返回
      if (status === 'loading') {
        return;
      }

      if (!session?.user?.id || !workspaceId) {
        setIsAdmin(false);
        setIsLoading(false);
        setError(null);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        // 获取用户在当前工作空间的角色
        const result = await getWorkspaceInfo(workspaceId);

        if (result.status === 'success' && result.data) {
          // 检查角色是否为 owner 或 admin
          const userRole = result.data.role;
          setIsAdmin(userRole === 'owner' || userRole === 'admin');
        } else {
          setIsAdmin(false);
          setError(result.message || 'Failed to get workspace role');
        }
      } catch (err) {
        console.error('Error checking workspace admin status:', err);
        setIsAdmin(false);
        setError('Failed to check admin status');
      } finally {
        setIsLoading(false);
      }
    }

    checkAdminStatus();
  }, [session, status, workspaceId]);

  return {
    isAdmin,
    isLoading,
    error
  };
}
