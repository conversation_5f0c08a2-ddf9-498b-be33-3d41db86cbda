"use client";
import { useState, useEffect } from "react";
import { signIn } from "next-auth/react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import Link from 'next/link';
import { Form, Input, Button, Alert, Divider } from 'antd';
import { GithubOutlined, GoogleOutlined } from '@ant-design/icons';
import GoogleLogo from "@/app/images/loginProvider/google.svg";
import logo from "@/app/images/logo.png";
import Hivechat from "@/app/images/hivechat.svg";
import { getActiveAuthProvides } from '@/app/(auth)/actions';
import SpinLoading from '@/app/components/loading/SpinLoading';
import { useTranslations } from 'next-intl';

interface LoginFormValues {
  email: string;
  password: string;
}

export default function LoginPage() {
  const t = useTranslations('Auth');
  const [form] = Form.useForm<LoginFormValues>();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [githubLoading, setGithubLoading] = useState(false);
  const [googleLoading, setGoogleLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(true);
  const [isRegistrationOpen, setIsRegistrationOpen] = useState(false);
  const [authProviders, setAuthProviders] = useState<string[]>([]);
  const [error, setError] = useState("");

  async function handleSubmit(values: LoginFormValues) {
    setLoading(true);
    const response = await signIn("credentials", {
      email: values.email,
      password: values.password,
      redirect: false,
    });
    setLoading(false);
    if (response?.error) {
      console.log(response?.error);
      setError(t('passwordError'));
      return;
    }
    router.push("/");
  }

  async function handleGithubLogin() {
    setGithubLoading(true);
    setError("");
    try {
      const response = await signIn("github", {
        redirect: false,
        callbackUrl: "/",
      });
      if (response?.error) {
        console.log("Github login error:", response.error);
        setError("Github 登录失败，请重试");
      } else if (response?.url) {
        router.push(response.url);
      } else {
        router.push("/");
      }
    } catch (error) {
      console.error("Github login error:", error);
      setError("Github 登录失败，请重试");
    } finally {
      setGithubLoading(false);
    }
  }

  async function handleGoogleLogin() {
    setGoogleLoading(true);
    setError("");
    try {
      const response = await signIn("google", {
        redirect: false,
        callbackUrl: "/",
      });
      if (response?.error) {
        console.log("Google login error:", response.error);
        setError("Google 登录失败，请重试");
      } else if (response?.url) {
        router.push(response.url);
      } else {
        router.push("/");
      }
    } catch (error) {
      console.error("Google login error:", error);
      setError("Google 登录失败，请重试");
    } finally {
      setGoogleLoading(false);
    }
  }

  useEffect(() => {
    const fetchSettings = async () => {
      const activeAuthProvides = await getActiveAuthProvides();
      setAuthProviders(activeAuthProvides)
    }
    fetchSettings().then(() => {
      setIsFetching(false);
    });
  }, []);

  if (isFetching) {
    return (
      <main className="h-dvh flex justify-center items-center">
        <SpinLoading />
        <span className='ml-2 text-gray-600'>Loading ...</span>
      </main>
    )
  }
  return (
    <div className="flex flex-col min-h-screen items-center justify-center bg-slate-50">
      <div className="flex items-center flex-row  mb-6">
        <Link href="/" className='flex items-center'>
          <Image src={logo} className="ml-1" alt="HiveChat logo" width={32} height={32} />
          <Hivechat className="ml-1" alt="HiveChat text" width={156} height={39} />
        </Link>
      </div>

      <div className="w-full  max-w-md rounded-lg bg-white p-8 shadow-xl">
        <h2 className="text-center text-2xl">{t('login')}</h2>
        {authProviders.includes('email') &&
          <>
            {error && <Alert message={error} type="error" />}
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSubmit}
              requiredMark='optional'
            >
              <Form.Item
                name="email"
                label={<span className="font-medium">Email</span>}
                validateTrigger='onBlur'
                rules={[{ required: true, type: 'email', message: t('emailNotice') }]}
              >
                <Input size='large' />
              </Form.Item>
              <Form.Item
                name="password"
                label={<span className="font-medium">{t('password')}</span>}
                rules={[{ required: true, message: t('passwordNotice') }]}
              >
                <Input.Password size='large' />
              </Form.Item>
              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  block
                  loading={loading}
                  size='large'
                >
                  {t('login')}
                </Button>
              </Form.Item>
              {isRegistrationOpen && <div className='flex -mt-4'>
                <Link href='/register'>
                  <Button
                    type='link'
                    className='text-sm text-gray-400'
                    style={{ 'padding': '0' }}
                  >{t('register')}</Button>
                </Link>
              </div>
              }
            </Form>
          </>
        }

        {/* 第三方登录按钮 */}
        {(authProviders.includes('github') || authProviders.includes('google')) && (
          <>
            {authProviders.includes('email') && (
              <Divider style={{ margin: '24px 0 16px 0' }}>
                <span style={{ color: '#666', fontSize: '14px' }}>或</span>
              </Divider>
            )}

            {/* Github 登录按钮 */}
            {authProviders.includes('github') && (
              <Button
                type="default"
                block
                size="large"
                loading={githubLoading}
                onClick={handleGithubLogin}
                icon={<GithubOutlined />}
                style={{ marginBottom: '12px' }}
              >
                使用 Github 登录
              </Button>
            )}

            {/* Google 登录按钮 */}
            {authProviders.includes('google') && (
              <Button
                type="default"
                block
                size="large"
                loading={googleLoading}
                onClick={handleGoogleLogin}
                icon={<GoogleLogo />}
                style={{ marginBottom: '12px' }}
              >
                使用 Google 登录
              </Button>
            )}
          </>
        )}
      </div>
    </div>
  );
}